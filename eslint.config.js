import globals from 'globals';
import pluginJs from '@eslint/js';
import tseslint, { plugin } from 'typescript-eslint';
import pluginReact from 'eslint-plugin-react';
import tsParser from '@typescript-eslint/parser';
import stylistic from '@stylistic/eslint-plugin';

export default [
  {
    plugins: {
      '@typescript-eslint': plugin,
      '@stylistic': stylistic,
    },
    files: ['**/*.{js,mjs,cjs,ts,jsx,tsx}'],
  },
  {
    languageOptions: {
      parser: tsParser,
      globals: globals.browser
    }
  },
  pluginJs.configs.recommended,
  ...tseslint.configs.recommended,
  pluginReact.configs.flat.recommended,
  {
    rules: {
      // basic
      //'no-console': ['error', { 'allow': ['error', 'info'] }],
      eqeqeq: ['error', 'always'],
      curly: 'error',
      'prefer-const': ['error'],
      'no-implicit-globals': ['error'],
      'no-duplicate-imports': ['error'],
      'no-await-in-loop': 'error', // prevents costly async loops
      'array-callback-return': ['error', { allowImplicit: false }],
      'no-unreachable': 'error',
      'no-empty': ['error', { allowEmptyCatch: true }],

      // react
      'react/react-in-jsx-scope': 'off',

      // ts lint
      '@typescript-eslint/no-explicit-any': 'warn',
      //'@typescript-eslint/explicit-function-return-type': 'error',
      '@typescript-eslint/no-unused-vars': ['error', { argsIgnorePattern: '^_' }],
      '@typescript-eslint/no-use-before-define': ['error', { functions: false, classes: true }],
      '@typescript-eslint/no-duplicate-enum-values': 'error',

      // stylistic
      '@stylistic/no-multi-spaces': 'error',
      '@stylistic/semi': ['error', 'always'],
      '@stylistic/quotes': ['error', 'single'], // revist to double if needed
      '@stylistic/no-extra-semi': ['error'],
      //'@stylistic/type-annotation-spacing': ['error', { 'before': true, 'after': true }],
      '@stylistic/object-curly-spacing': ['error', 'always']
    }
  }
];