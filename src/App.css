@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

html {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: stonescale;
}

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.145 0 0);
  --primary-foreground: oklch(1 0 0);
  --secondary: oklch(0.9 0 0);
  --secondary-foreground: oklch(0.145 0 0);
  --muted: oklch(0.95 0 0);
  --muted-foreground: oklch(0.5 0 0);
  --accent: oklch(0.95 0 0);
  --accent-foreground: oklch(0.145 0 0);
  --destructive: oklch(0.3 0 0);
  --border: oklch(0.9 0 0);
  --input: oklch(0.9 0 0);
  --ring: oklch(0.7 0 0);
  --chart-1: oklch(0.1 0 0);
  --chart-2: oklch(0.3 0 0);
  --chart-3: oklch(0.5 0 0);
  --chart-4: oklch(0.7 0 0);
  --chart-5: oklch(0.9 0 0);
  --sidebar: oklch(1 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.145 0 0);
  --sidebar-primary-foreground: oklch(1 0 0);
  --sidebar-accent: oklch(0.95 0 0);
  --sidebar-accent-foreground: oklch(0.145 0 0);
  --sidebar-border: oklch(0.9 0 0);
  --sidebar-ring: oklch(0.7 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.2 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.2 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.9 0 0);
  --primary-foreground: oklch(0.145 0 0);
  --secondary: oklch(0.3 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.3 0 0);
  --muted-foreground: oklch(0.7 0 0);
  --accent: oklch(0.3 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.7 0 0);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.5 0 0);
  --chart-1: oklch(0.9 0 0);
  --chart-2: oklch(0.7 0 0);
  --chart-3: oklch(0.5 0 0);
  --chart-4: oklch(0.3 0 0);
  --chart-5: oklch(0.1 0 0);
  --sidebar: oklch(0.2 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.9 0 0);
  --sidebar-primary-foreground: oklch(0.145 0 0);
  --sidebar-accent: oklch(0.3 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.5 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    margin: 0;
    min-width: 100%;
    min-height: 100vh;
    width: 100%;
    overflow-x: hidden;
  }
  h1 {
    font-size: 3.2em;
    line-height: 1.1;
  }
  a {
    font-weight: 500;
    text-decoration: inherit;
  }
}
