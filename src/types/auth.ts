export type UserRole = 'aupair' | 'family' | 'admin' | 'agent' | 'support' | 'support.admin';

export type User = {
  id: string;
  email?: string;
  user_metadata?: {
    full_name?: string;
    avatar_url?: string;
  };
};

export type Profile = {
  id : string;
  email : string;
  role : UserRole;
  full_name : string;
  avatar_url ?: string;
};

export type AuthUser = {
  id: string;
  email: string;
  role: UserRole;
  full_name: string;
  avatar_url?: string;
};

// To do Fix camel case and use interface

export type AuthContextType = {
  user: User | null | undefined;
  profile: Profile | null | undefined;
  authUser: AuthUser | null | undefined;
  isUserLoading: boolean;
  isProfileLoading: boolean;
  isSigningIn: boolean;
  isSigningOut: boolean;
  isAuthenticated: boolean;
  isAdmin: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signOut: () => Promise<void>;
  error: Error | null;
};
