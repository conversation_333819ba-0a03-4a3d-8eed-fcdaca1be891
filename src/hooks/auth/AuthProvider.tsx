import { type ReactNode } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/api/supabase';
import { AuthContext } from './AuthContext';
import { type User, type Profile } from '@/types/auth';

type AuthProviderProps = {
  children: ReactNode;
};

export function AuthProvider({ children }: AuthProviderProps) {
  const queryClient = useQueryClient();

  const {
    data: user,
    isLoading: isUserLoading,
    error: userError,
  } = useQuery({
    queryKey: ['auth', 'session'],
    queryFn: async (): Promise<User | null> => {
      const { data, error } = await supabase.auth.getSession();
      if (error) {throw error;}
      return data?.session?.user ? (data.session.user as User) : null;
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
  });

  const {
    data: profile,
    isLoading: isProfileLoading,
    error: profileError,
  } = useQuery({
    queryKey: ['profile', user?.id],
    queryFn: async (): Promise<Profile | null> => {
      if (!user) {return null;}
      const { data, error } = await supabase.from('profiles').select('*').eq('id', user.id).single();
      if (error) {throw error;}
      return data;
    },
    enabled: !!user,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });

  const isAdmin = profile?.role === 'admin';

  const signInMutation = useMutation({
    mutationFn: async ({ email, password }: { email: string; password: string }) => {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });
      if (error) {throw error;}
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', data.user.id)
        .single();

      if (profileError || profile?.role !== 'admin') {
        await supabase.auth.signOut({ scope: 'local' });
        throw new Error('Access denied: You must be an admin');
      }

      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['auth'] });
    },
  });

  const signOutMutation = useMutation({
    mutationFn: async () => {
      const { error } = await supabase.auth.signOut({ scope: 'local' });
      if (error && !error.message.includes('Auth session missing')) {
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries();
      queryClient.clear();
    },
  });

  const error = userError || profileError;
  const isAuthenticated = !!user && isAdmin;

  return (
    <AuthContext.Provider
      value={{
        user,
        profile,
        authUser: profile
          ? {
              id: profile.id,
              email: profile.email,
              role: profile.role,
              full_name: profile.full_name,
              avatar_url: profile.avatar_url,
            }
          : undefined,
        isUserLoading,
        isProfileLoading,
        isSigningIn: signInMutation.isPending,
        isSigningOut: signOutMutation.isPending,
        isAuthenticated,
        isAdmin,
        signIn: async (email, password) => {
          await signInMutation.mutateAsync({ email, password });
        },
        signOut: async () => {
          await signOutMutation.mutateAsync();
        },
        error: error as Error | null,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}
