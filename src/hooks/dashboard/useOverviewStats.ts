import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/api/supabase';

export function useOverviewStats() {
  return useQuery({
    queryKey: ['dashboard', 'stats'],
    queryFn: async () => {
      const [familiesResult, auPairsResult, matchesResult, suggestedResult] = await Promise.all([
        supabase.from('profiles').select('*', { count: 'exact', head: true }).eq('role', 'family'),
        supabase.from('profiles').select('*', { count: 'exact', head: true }).eq('role', 'aupair'),
        supabase.from('matches').select('*', { count: 'exact', head: true }).eq('status', 'confirmed'),
        supabase.from('matches').select('*', { count: 'exact', head: true }).eq('status', 'suggested'),
      ]);

      return {
        families: familiesResult.count || '0',
        auPairs: auPairsResult.count || '0',
        matches: matchesResult.count || '0',
        suggested: suggestedResult.count || '0',
      };
    },
    refetchInterval: 30000
  });
}
