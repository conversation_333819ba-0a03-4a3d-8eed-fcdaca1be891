import React from 'react';
import ReactDOM from 'react-dom/client';
import { createBrowserRouter, RouterProvider } from 'react-router';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import MainLayout from './app/_layout';
import ActivityPage from './app/dashboard/activity/page';
import SettingsPage from './app/dashboard/settings/page';
import HelpPage from './app/dashboard/help/page';
import AuPairsPage from './app/dashboard/aupairs/page';
import FamiliesPage from './app/dashboard/families/page';
import MatchingPage from './app/dashboard/matching/page';
import './App.css';
import OverviewPage from './app/dashboard/page';
import { AuthProvider } from './hooks/auth';
import { HomeWithAuthRedirect } from './routes/protected/HomeWithAuthRedirect';
import { ProtectedDashboardLayout } from './routes/protected/ProtectedDashboardLayout';
import FamilyJourney from './app/dashboard/families/familyDetails/[id]/familyJourney/page';
import FamilyDetailsPage from './app/dashboard/families/familyDetails/[id]/page';
import AuPairDetailsPage from './app/dashboard/aupairs/aupairDetails/[id]/page.tsx';

// login-homepage
const router = createBrowserRouter([
  {
    path: '/',
    Component: MainLayout,
    children: [{ index: true, Component: HomeWithAuthRedirect }],
  },
  // protected routes - routes/protected
  {
    path: '/dashboard',
    Component: ProtectedDashboardLayout,
    children: [
      { index: true, Component: OverviewPage },
      { path: 'activity', Component: ActivityPage },
      { path: 'au-pairs', Component: AuPairsPage },
      { path: 'families', Component: FamiliesPage },
      { path: 'matching', Component: MatchingPage },
      { path: 'settings', Component: SettingsPage },
      { path: 'help', Component: HelpPage },
      
      // family routes
      { path: 'families/:id', Component: FamilyDetailsPage },
      { path: 'families/journey/:id', Component: FamilyJourney },

      // au pair routes
      { path: 'au-pairs/:id', Component: AuPairDetailsPage },
    ],
  },
]);

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
      staleTime: 60000
    },
    mutations: {

    }
  },
});

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <RouterProvider router={router} />
      </AuthProvider>
    </QueryClientProvider>
  </React.StrictMode>
);
