import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Clock, Upload } from 'lucide-react';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';

interface LegalResidenceStepProps {
  onSubmit?: () => void;
  isCompleted?: boolean;
}

export default function LegalResidenceStep({ onSubmit, isCompleted = false }: LegalResidenceStepProps) {
  const handleSubmit = () => {
    if (onSubmit) {
      onSubmit();
    }
    console.log('Legal residence submitted');
  };

  return (
    <div className="space-y-8">
      <Accordion type="single" collapsible defaultValue="item-1" className="space-y-4">
        <AccordionItem value="item-1" className="border-2 border-stone-200 rounded-md">
          <AccordionTrigger className="text-left px-6 py-4 hover:no-underline">
            <div className="flex items-center space-x-4">
              <Clock className={`h-6 w-6 ${isCompleted ? 'text-black' : 'text-stone-600'}`} />
              <span className="text-lg font-semibold">3.2 Legal Residence</span>
              <Badge className={isCompleted ? 'bg-black text-white' : 'bg-stone-600 text-white'}>
                {isCompleted ? 'Completed' : 'In Progress'}
              </Badge>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6">
            <Card className="border-0 shadow-lg">
              <CardContent className="pt-8">
                <div className="space-y-8">
                  <div>
                    <h4 className="font-bold text-black text-base mb-3">Law:</h4>
                    <p className="text-stone-700 text-base leading-relaxed">
                      According to IND regulations, a valid copy of the host parent&apos;s passport is required when applying
                      for an au pair residence permit.
                    </p>
                  </div>

                  <div className="grid md:grid-cols-2 gap-8">
                    <div className="bg-black text-white p-8 rounded-md">
                      <h3 className="text-base font-bold mb-6">Passport Parent 1</h3>
                      <div className="space-y-6">
                        <div className="border-2 border-dashed border-white/30 rounded-md p-6 text-center">
                          <Upload className="h-8 w-8 mx-auto mb-3 text-white/70" />
                          <p className="text-sm text-white/90">Upload passport</p>
                        </div>
                        <div>
                          <Label htmlFor="bsn1" className="text-white text-base font-semibold">
                            BSN NUMBER
                          </Label>
                          <Input
                            id="bsn1"
                            placeholder="Enter BSN number"
                            className="mt-2 bg-white/10 border-white/20 text-white placeholder:text-white/70 text-base"
                          />
                        </div>
                      </div>
                    </div>

                    <div className="bg-stone-900 text-white p-8 rounded-md">
                      <h3 className="text-base font-bold mb-6">Passport Parent 2</h3>
                      <div className="space-y-6">
                        <div className="border-2 border-dashed border-white/30 rounded-md p-6 text-center">
                          <Upload className="h-8 w-8 mx-auto mb-3 text-white/70" />
                          <p className="text-sm text-white/90">Upload passport</p>
                        </div>
                        <div>
                          <Label htmlFor="bsn2" className="text-white text-base font-semibold">
                            BSN NUMBER
                          </Label>
                          <Input
                            id="bsn2"
                            placeholder="Enter BSN number"
                            className="mt-2 bg-white/10 border-white/20 text-white placeholder:text-white/70 text-lg"
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  <Button
                    className="w-full bg-black text-white hover:bg-stone-800 text-lg py-3"
                    onClick={handleSubmit}
                    disabled={isCompleted}
                  >
                    SUBMIT
                  </Button>
                </div>
              </CardContent>
            </Card>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
}
