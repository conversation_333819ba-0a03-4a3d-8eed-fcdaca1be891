import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, Download, ExternalLink } from 'lucide-react';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { useState, useEffect } from 'react';

interface AssignmentConfirmationStepProps {
  onSubmit?: () => void;
  isCompleted?: boolean;
  isExpanded?: boolean;
}

export default function AssignmentConfirmationStep({
  onSubmit,
  isCompleted = false,
  isExpanded = false,
}: AssignmentConfirmationStepProps) {
  const [accordionValue, setAccordionValue] = useState<string>('');

  const handleSubmit = () => {
    if (onSubmit) {
      onSubmit();
    }
    console.log('Assignment confirmation submitted');
  };

  // Update accordion state when isExpanded prop changes (from sidebar navigation)
  useEffect(() => {
    if (isExpanded) {
      setAccordionValue('item-1');
    }
  }, [isExpanded]);

  return (
    <div className="space-y-8">
      <Accordion
        type="single"
        collapsible
        value={accordionValue}
        onValueChange={setAccordionValue}
        className="space-y-4"
      >
        <AccordionItem value="item-1" className="border-2 border-stone-200 rounded-md">
          <AccordionTrigger className="text-left px-6 py-4 hover:no-underline">
            <div className="flex items-center space-x-4">
              <CheckCircle className={`h-6 w-6 ${isCompleted ? 'text-black' : 'text-stone-600'}`} />
              <span className="text-base font-semibold">1.2 Assignment Confirmation</span>
              <Badge className={isCompleted ? 'bg-black text-white' : 'bg-stone-600 text-white'}>
                {isCompleted ? 'Completed' : 'In Progress'}
              </Badge>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6">
            <Card className="border-0 shadow-lg">
              <CardContent className="pt-8">
                <div className="grid md:grid-cols-3 gap-8">
                  <div className="md:col-span-2 space-y-6">
                    <p className="text-stone-700 text-base leading-relaxed">
                      Please read the Assignment Confirmation including Data Processing Agreement (AVG), our general
                      terms and conditions, the IND brochure and HBN fees carefully, in order for you to confirm the
                      assignment confirmation here.
                    </p>

                    <div className="space-y-4">
                      <h4 className="font-bold text-black text-base">To Do:</h4>
                      <ul className="space-y-3 text-stone-700">
                        <li className="flex items-center space-x-3 p-3 border border-stone-200 rounded-md">
                          <ExternalLink className="h-5 w-5 text-black" />
                          <span className="flex-1">IND Brochure Cultural Exchange NL</span>
                          <Button
                            variant="outline"
                            size="sm"
                            className="border-black text-black hover:bg-black hover:text-white"
                          >
                            Download
                          </Button>
                        </li>
                        <li className="flex items-center space-x-3 p-3 border border-stone-200 rounded-md">
                          <ExternalLink className="h-5 w-5 text-black" />
                          <span className="flex-1">IND Brochure Cultural Exchange ENG</span>
                          <Button
                            variant="outline"
                            size="sm"
                            className="border-black text-black hover:bg-black hover:text-white"
                          >
                            Download
                          </Button>
                        </li>
                        <li className="flex items-center space-x-3 p-3 border border-stone-200 rounded-md">
                          <ExternalLink className="h-5 w-5 text-black" />
                          <span className="flex-1">HBN Terms & Conditions</span>
                          <Button
                            variant="outline"
                            size="sm"
                            className="border-black text-black hover:bg-black hover:text-white"
                          >
                            View
                          </Button>
                        </li>
                      </ul>
                    </div>

                    <div className="flex items-center space-x-3 p-6 bg-stone-50 rounded-md border-2 border-stone-200">
                      <Checkbox id="confirm-assignment" 
                      //checked={isCompleted} 
                      //disabled={isCompleted} 
                      />
                      <label htmlFor="confirm-assignment" className="text-base font-semibold text-black">
                        Confirm Assignment
                      </label>
                    </div>
                    <p className="text-sm text-stone-500">
                      By clicking on this button, you agree to the assignment and our Terms & Conditions
                    </p>
                  </div>

                  <div className="bg-black text-white p-8 rounded-md">
                    <h3 className="text-base font-bold mb-6">Assignment Confirmation</h3>
                    <p className="text-sm mb-6 text-stone-300">
                      <strong>NOTE:</strong> Pop-up may be blocked, please allow pop-ups for this website.
                    </p>
                    <Button
                      className="w-full bg-white text-black hover:bg-stone-600"
                      // onClick={handleSubmit}
                     //  disabled={isCompleted}
                    >
                      <Download className="h-4 w-4 mr-2" />
                      DOWNLOAD
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
}
