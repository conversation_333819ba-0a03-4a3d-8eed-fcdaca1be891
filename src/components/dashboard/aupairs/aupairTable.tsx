import { useState } from 'react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { MoreHorizontal, Search, ArrowUpDown } from 'lucide-react';
import { Link } from 'react-router';
import { Badge } from '@/components/ui/badge';

// Mock data for au pairs
const aupairs = [
  {
    id: '1',
    firstName: 'Maria',
    lastName: 'Schmidt',
    email: '<EMAIL>',
    phone: '+49 123 456789',
    country: 'Germany',
    dateOfBirth: '1998-05-15',
    earliestStartDate: '2023-09-01',
    latestStartDate: '2025-04-04',
    status: 'available',
  },
  {
    id: '2',
    firstName: 'Sofia',
    lastName: '<PERSON>',
    email: '<EMAIL>',
    phone: '+34 987 654321',
    country: 'Spain',
    dateOfBirth: '1999-03-22',
    earliestStartDate: '2023-08-15',
    latestStartDate: '2025-04-04',
    status: 'placed',
  },
  {
    id: '3',
    firstName: 'Anna',
    lastName: 'Kowalski',
    email: '<EMAIL>',
    phone: '+48 123 456 789',
    country: 'Poland',
    dateOfBirth: '2000-01-10',
    earliestStartDate: '2023-07-01',
    latestStartDate: '2025-04-04',
    status: 'in-process',
  },
  {
    id: '4',
    firstName: 'Emma',
    lastName: 'Johnson',
    email: '<EMAIL>',
    phone: '+44 7700 900123',
    country: 'United Kingdom',
    dateOfBirth: '1997-11-30',
    earliestStartDate: '2023-10-01',
    latestStartDate: '2023-04-04',
    status: 'available',
  },
  {
    id: '5',
    firstName: 'Luisa',
    lastName: 'Bianchi',
    email: '<EMAIL>',
    phone: '+39 333 1234567',
    country: 'Italy',
    dateOfBirth: '1998-08-05',
    earliestStartDate: '2023-09-15',
    latestStartDate: '2024-04-04',
    status: 'in-process',
  },
];

export function AupairTable() {
  const [searchTerm, setSearchTerm] = useState('');
  const [sortColumn, setSortColumn] = useState('lastName');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [currentPage, setCurrentPage] = useState(1);
  const rowsPerPage = 5;

  const handleSort = (column: string) => {
    if (sortColumn === column) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortColumn(column);
      setSortDirection('asc');
    }
  };

  const filteredAupairs = aupairs
    .filter(
      aupair =>
        aupair.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        aupair.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        aupair.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        aupair.country.toLowerCase().includes(searchTerm.toLowerCase())
    )
    .sort((a, b) => {
      const aValue = a[sortColumn as keyof typeof a];
      const bValue = b[sortColumn as keyof typeof b];

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return sortDirection === 'asc' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
      }

      return 0;
    });

  // Pagination logic
  const totalRows = filteredAupairs.length;
  const totalPages = Math.ceil(totalRows / rowsPerPage);
  const paginatedAupairs = filteredAupairs.slice((currentPage - 1) * rowsPerPage, currentPage * rowsPerPage);

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'available':
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Available</Badge>;
      case 'placed':
        return <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100">Placed</Badge>;
      case 'in-process':
        return <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">In Process</Badge>;
      default:
        return null;
    }
  };

  return (
    /* Search bar*/
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="relative w-64">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search au pairs..."
            className="pl-8"
            value={searchTerm}
            onChange={e => setSearchTerm(e.target.value)}
          />
        </div>
      </div>
      {/* Desktop Table Layout */}
      <div className="rounded-md border hidden md:block">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[250px]">
                <Button variant="ghost" className="p-0 font-medium" onClick={() => handleSort('lastName')}>
                  Name
                  <ArrowUpDown className="ml-2 h-4 w-4" />
                </Button>
              </TableHead>
              <TableHead>
                <Button variant="ghost" className="p-0 font-medium">
                  Contact Details
                </Button>
              </TableHead>
              <TableHead>
                <Button variant="ghost" className="p-0 font-medium" onClick={() => handleSort('country')}>
                  Country
                  <ArrowUpDown className="ml-2 h-4 w-4" />
                </Button>
              </TableHead>
              <TableHead>
                <Button variant="ghost" className="p-0 font-medium" onClick={() => handleSort('earliestStartDate')}>
                  Earliest Start
                  <ArrowUpDown className="ml-2 h-4 w-4" />
                </Button>
              </TableHead>
              <TableHead>
                <Button variant="ghost" className="p-0 font-medium" onClick={() => handleSort('latestStartDate')}>
                  Latest Start
                  <ArrowUpDown className="ml-2 h-4 w-4" />
                </Button>
              </TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="w-[70px]"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {paginatedAupairs.map(aupair => (
              <TableRow key={aupair.id}>
                <TableCell>
                  <Link to={`/dashboard/au-pairs/${aupair.id}`} className="font-medium hover:underline">
                    {aupair.firstName} {aupair.lastName}
                  </Link>
                </TableCell>
                <TableCell>
                  <div className="flex flex-col">
                    <span>{aupair.email}</span>
                    <span className="text-muted-foreground text-sm">{aupair.phone}</span>
                  </div>
                </TableCell>
                <TableCell>{aupair.country}</TableCell>
                <TableCell>{new Date(aupair.earliestStartDate).toLocaleDateString()}</TableCell>
                <TableCell>{new Date(aupair.latestStartDate).toLocaleDateString()}</TableCell>
                <TableCell>{getStatusBadge(aupair.status)}</TableCell>
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <span className="sr-only">Open menu</span>
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem>
                        <Link to={`/dashboard/au-pairs/${aupair.id}`} className="flex w-full">
                          View details
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Link to={`/dashboard/au-pairs/${aupair.id}/edit`} className="flex w-full">
                          Edit
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem className="text-red-600">Delete</DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Mobile Card Layout */}
      <div className="block md:hidden space-y-4">
        {paginatedAupairs.map(aupair => (
          <div key={aupair.id} className="rounded-md border p-4 shadow-sm bg-white">
            <div className="flex items-center justify-between mb-2">
              <span className="font-semibold text-lg">
                <Link to={`/dashboard/au-pairs/${aupair.id}`} className="hover:underline">
                  {aupair.firstName} {aupair.lastName}
                </Link>
              </span>
              <span>{getStatusBadge(aupair.status)}</span>
            </div>
            <div className="mb-1">
              <span className="block text-xs font-semibold text-muted-foreground">Email</span>
              <span className="block break-all">{aupair.email}</span>
            </div>
            <div className="mb-1">
              <span className="block text-xs font-semibold text-muted-foreground">Phone</span>
              <span className="block">{aupair.phone}</span>
            </div>
            <div className="mb-1">
              <span className="block text-xs font-semibold text-muted-foreground">Country</span>
              <span className="block">{aupair.country}</span>
            </div>
            <div className="mb-1">
              <span className="block text-xs font-semibold text-muted-foreground">Earliest Start</span>
              <span className="block">{new Date(aupair.earliestStartDate).toLocaleDateString()}</span>
            </div>
            <div className="flex gap-2 mt-3">
              <Link
                to={`/dashboard/au-pairs/${aupair.id}`}
                className="text-blue-600 text-sm font-medium hover:underline"
              >
                View
              </Link>
              <Link
                to={`/dashboard/au-pairs/${aupair.id}/edit`}
                className="text-yellow-600 text-sm font-medium hover:underline"
              >
                Edit
              </Link>
              <button className="text-red-600 text-sm font-medium">Delete</button>
            </div>
          </div>
        ))}
      </div>
      {/* Pagination Controls */}
      <div className="flex justify-end items-center gap-2 mt-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() => setCurrentPage(p => Math.max(1, p - 1))}
          disabled={currentPage === 1}
        >
          Prev
        </Button>
        {Array.from({ length: totalPages }, (_, i) => (
          <Button
            key={i + 1}
            variant={currentPage === i + 1 ? 'default' : 'outline'}
            size="sm"
            onClick={() => setCurrentPage(i + 1)}
          >
            {i + 1}
          </Button>
        ))}
        <Button
          variant="outline"
          size="sm"
          onClick={() => setCurrentPage(p => Math.min(totalPages, p + 1))}
          disabled={currentPage === totalPages || totalPages === 0}
        >
          Next
        </Button>
      </div>
    </div>
  );
}
